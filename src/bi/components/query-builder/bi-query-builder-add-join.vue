<script setup>
import { ref } from 'vue';
import { useBIQueryBuilder } from '~/bi/composables/useBIQueryBuilder.js';

defineProps({
  primaryDataset: {
    type: Object,
    default: () => ({}),
  },
  selectedTables: {
    type: Array,
    default: () => [],
  },
  allTables: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['save']);
const form$ = ref(null);
const { getIconsForType } = useBIQueryBuilder();
const form_data = ref(null);
const join_types = getIconsForType('joins');

const conditions = ref([{ label: '=', uid: '=' }, { label: '>', uid: '>' }, { label: '<', uid: '<' }, { label: '>=', uid: '>=' }, { label: '<=', uid: '<=' }]);
const selected_condition = ref(conditions.value[0]);

const types = ref([{ label: 'Inner Join', uid: 'inner', icon: join_types.inner }, { label: 'Outer Join', uid: 'outer', icon: join_types.outer }, { label: 'Right Join', uid: 'right', icon: join_types.right }, { label: 'Left Join', uid: 'left', icon: join_types.left }]);
const selected_type = ref(types.value[0]);

async function save(form$, close) {
  await form$.validate();
  if (form$.invalid)
    return;

  const data = form$.data;
  emit('save', {
    label: data?.secondary_dataset?.label,
    columns: data?.secondary_dataset.columns,
    type: selected_type.value.uid,
    condition: selected_condition.value.uid,
    on: { left: data.primary_dataset_column, right: data.secondary_dataset_column },
  });
  close();
}
</script>

<template>
  <hawk-menu position="fixed">
    <template #trigger>
      <div>
        <slot>
          <hawk-button type="text" size="xs" icon>
            <IconHawkVectorJoin class="size-4" />
          </hawk-button>
        </slot>
      </div>
    </template>
    <template #content="{ close }">
      <div class="z-20 bg-white border w-[600px]  flex flex-col">
        <Vueform ref="form$" v-model="form_data" sync size="sm" :display-errors="false">
          <div class="col-span-12">
            <div class="w-full font-medium p-4 flex items-center justify-between border-b ">
              <div class="flex items-center gap-2">
                {{ $t('Join datasets') }}
              </div>
              <div class="flex items-center gap-2" @click="close">
                <hawk-button icon type="text">
                  <IconHawkXClose class="text-gray-500 size-4" />
                </hawk-button>
              </div>
            </div>
            <div class="flex-1 p-4">
              <div class="col-span-12">
                <div class="text-sm font-medium">
                  {{ $t('Join') }}
                </div>
                <div class="grid grid-cols-12 items-center mt-2">
                  <SelectElement
                    name="primary_dataset" :items="[primaryDataset]"
                    :default="primaryDataset.label"
                    :can-clear="false"
                    value-prop="label"
                    rules="required"
                    disabled
                    object
                    :columns="{
                      default: { container: 5 },
                      sm: { container: 5 },
                      md: { container: 5 },
                      lg: { container: 5 },
                    }"
                  />
                  <hawk-menu class="col-span-2 text-center" :items="types" @select="selected_type = $event">
                    <template #trigger>
                      <hawk-button type="text" icon>
                        <component :is="selected_type.icon" class="size-5" />
                      </hawk-button>
                    </template>
                    <template #item="{ item }">
                      <div class="flex">
                        <component :is="item.icon" class="size-5 mr-2" />
                        {{ item.label }}
                      </div>
                    </template>
                  </hawk-menu>
                  <SelectElement
                    name="secondary_dataset"
                    :items="allTables"
                    value-prop="label"
                    :default="allTables[0]"
                    rules="required"
                    object
                    :can-clear="false"
                    :native="false"
                    :columns="{
                      default: { container: 5 },
                      sm: { container: 5 },
                      md: { container: 5 },
                      lg: { container: 5 },
                    }"
                  />
                </div>
                <div class="mt-4 text-sm font-medium">
                  {{ $t('On') }}
                </div>
                <div class="grid grid-cols-12 items-center mt-2">
                  <SelectElement
                    name="primary_dataset_column"
                    value-prop="label"
                    :items="primaryDataset?.columns"
                    rules="required"
                    :native="false"
                    :can-clear="false"
                    :columns="{
                      default: { container: 5 },
                      sm: { container: 5 },
                      md: { container: 5 },
                      lg: { container: 5 },
                    }"
                  />
                  <hawk-menu class="col-span-2 text-center" :items="conditions" @select="selected_condition = $event">
                    <template #trigger>
                      <hawk-button type="text" icon>
                        {{ selected_condition.label }}
                      </hawk-button>
                    </template>
                  </hawk-menu>
                  <SelectElement
                    name="secondary_dataset_column"
                    :native="false"
                    value-prop="label"
                    :can-clear="false"
                    rules="required"
                    :items="form_data?.secondary_dataset?.columns || []" :columns="{
                      default: { container: 5 },
                      sm: { container: 5 },
                      md: { container: 5 },
                      lg: { container: 5 },
                    }"
                  />
                </div>
              </div>
            </div>
            <div class="flex w-full items-center justify-between p-4 border-t">
              <div />
              <div class="flex items-center gap-2">
                <hawk-button type="outlined" @click="close">
                  {{ $t('Cancel') }}
                </hawk-button>
                <hawk-button
                  name="submit"
                  @click="save(form$, close)"
                >
                  {{ $t('Save') }}
                </hawk-button>
              </div>
            </div>
          </div>
        </Vueform>
      </div>
    </template>
  </hawk-menu>
</template>
