<!-- eslint-disable vue/prop-name-casing -->
<script setup>
const props = defineProps({
  field: {
    type: String,
    default: 'Delete',
  },
});

const emit = defineEmits(['close', 'save']);
const $t = inject('$t');
</script>

<template>
  <hawk-modal-container content_class="w-[400px] rounded-lg" :options="props.modalOptions">
    <div class="flex items-center p-6 justify-between text-lg font-semibold text-gray-800 ">
      <div>
        {{ field.label }}
      </div>
      <div class="flex items-center justify-center">
        <div class="text-gray-600 rounded-md hover:bg-gray-50 cursor-pointer flex justify-center items-center p-2" @click="emit('close')">
          <IconHawkXClose class="w-6 h-6" />
        </div>
      </div>
    </div>
    <hawk-modal-content class="border-t">
      <div class="grid gap-2">
        <div>
          <div class="text-sm font-medium text-gray-700">
            {{ $t('Breakdown By') }}
          </div>
        </div>
        <div>
          <div class="text-sm font-medium text-gray-700">
            {{ $t('Sort By') }}
          </div>
        </div>
        <div>
          <div class="text-sm font-medium text-gray-700">
            {{ $t('Frame') }}
          </div>
        </div>
      </div>
    </hawk-modal-content>
    <hawk-modal-footer>
      <template #right>
        <div class="flex justify-end">
          <HawkButton color="primary" type="outlined" class="mr-3" @click="$emit('close')">
            {{ $t('Cancel') }}
          </HawkButton>
          <HawkButton
            @click="emit('save')"
          >
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </template>
    </hawk-modal-footer>
  </hawk-modal-container>
</template>
