<script setup>
import { cloneDeep } from 'lodash-es';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  tables: {
    type: Array,
    default: () => ([]),
  },
  fields: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['selected', 'expression']);
const { $t } = useCommonImports();

function onColumnsSelected(field) {
  emit('selected', field);
}

function onExpressionClicked(field) {
  emit('expression', field);
}

const is_dropdown_open = ref(false);

const getLabel = field => field.agg ? `${field.agg} ${$t('of')} ${field.label}` : field.label;

const tables_with_fields = computed(() => {
  const tables = cloneDeep(props.tables);
  const table = tables[0];
  table.columns = [...table.columns, ...props.fields.filter(field => field.agg || field.expression).map(field => ({ label: getLabel(field), type: field.type, key: field.key }))];
  return tables;
});
</script>

<template>
  <div v-click-outside="() => { is_dropdown_open = false }">
    <div @click="is_dropdown_open = true">
      <slot>
        <hawk-button>
          <IconHawkPlus />  {{ $t('Add columns') }}
        </hawk-button>
      </slot>
    </div>
    <div v-if="is_dropdown_open" class="fixed min-w-[300px] z-20">
      <bi-query-builder-columns-dropdown :tables="tables_with_fields" :is-dropdown="true" :has-functions="false" @selected="onColumnsSelected" @expression="onExpressionClicked" />
    </div>
  </div>
</template>
