<script setup>
import <PERSON> from 'papaparse';
import { v4 as uuidv4 } from 'uuid';
import { computed, onMounted, reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';
import BiHandsontable from './bi-handsontable.vue';

const props = defineProps({
  id: {
    type: String,
    default: () => uuidv4(),
  },
  dataConfig: {
    type: Object,
    default: () => ({}),
  },
});

const bi_store = useBiStore();

const bi_table_ref = ref(null);
const state = reactive({
  loading: false,
  error: null,
  csv_data: [],
  columns: [],

  table_columns: [],
  table_data: [],
  nested_headers: [],
  nested_rows: true,
});

const table_columns = computed(() => {
  return state.table_columns.map(column => ({
    data: column,
    title: column,
    type: 'text',
    readOnly: true,
    width: props.dataConfig[column]?.width || 150,
  }));
});

const column_config = computed(() => {
  // Apply column-based coloring based on dataConfig
  const config = {};

  return config;
});

// -------------------------------- Methods --------------------------------- //
async function fetchProgressHistoryData() {
  try {
    state.loading = true;
    state.error = null;

    // Fetch the CSV file from the public directory
    const csvPath = '/progress-history.csv';

    // For demo purposes, we'll simulate fetching from the actual file
    // In a real implementation, you might fetch from an API endpoint
    const response = await fetch(csvPath);

    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }

    const csvText = await response.text();

    // Parse CSV using Papa Parse
    const parseResult = Papa.parse(csvText, {
      header: true,
      skipEmptyLines: true,
      transformHeader: header => header.trim(),
    });

    if (parseResult.errors.length > 0) {
      console.warn('CSV parsing warnings:', parseResult.errors);
    }

    // Extract columns from the first row (headers)
    state.columns = parseResult.meta.fields || [];
    bi_store.table_config.columns = state.columns;

    // Process the data
    state.csv_data = parseResult.data.slice(0, 100000);

    // Apply any data formatting and grouping
    state.table_columns = ['Sub Activity', 'Activity', 'Layer', 'Sub Layer', 'Date', 'User', 'Value'];
    bi_store.table_preview_config.columns = ['Layer', 'Sub Layer'];
    bi_store.table_preview_config.rows = ['Activity', 'Sub Activity'];
    bi_store.table_preview_config.values = ['Date', 'User', 'Value'];
    state.table_data = state.csv_data;
    const nested_headers = generateNestedHeadersWithObjects(state.csv_data, bi_store.table_preview_config.columns, bi_store.table_preview_config.values, bi_store.table_preview_config.rows);
    console.log(nested_headers);
  }
  catch (error) {
    console.error('Error fetching progress history data:', error);
    state.error = error.message;
  }
  finally {
    state.loading = false;
  }
}

function generateNestedHeadersWithObjects(data, columns, values, rowHeaders = [], delimiter = '|') {
  const nestedHeaders = [];

  function buildTree(data, level = 0) {
    if (level >= columns.length)
      return [];

    const groups = {};
    for (const row of data) {
      const key = row[columns[level]];
      if (!groups[key])
        groups[key] = [];
      groups[key].push(row);
    }

    const nodes = [];
    for (const key in groups) {
      const children = buildTree(groups[key], level + 1);
      nodes.push({
        label: key,
        children,
      });
    }

    return nodes;
  }

  const tree = buildTree(data);

  function countLeaves(node) {
    if (!node.children || node.children.length === 0)
      return 1;
    return node.children.reduce((sum, child) => sum + countLeaves(child), 0);
  }

  function fillHeaders(nodes, level = 0) {
    if (!nestedHeaders[level]) {
      nestedHeaders[level] = [];

      // Prepend row header space once per level
      if (rowHeaders.length > 0) {
        nestedHeaders[level].push({ label: '', colspan: 1 });
      }
    }

    for (const node of nodes) {
      const leafCount = countLeaves(node);
      nestedHeaders[level].push({
        label: node.label,
        colspan: leafCount * values.length,
      });

      if (node.children.length > 0) {
        fillHeaders(node.children, level + 1);
      }
    }
  }

  fillHeaders(tree);

  // Build leaf labels as {label, key} objects with full path keys
  const finalRow = [];

  // First cell for row header
  if (rowHeaders.length > 0) {
    finalRow.push(rowHeaders[0]);
  }

  function pushLeafLabelsWithObjects(nodes, path = []) {
    for (const node of nodes) {
      const currentPath = [...path, node.label];
      if (node.children.length > 0) {
        pushLeafLabelsWithObjects(node.children, currentPath);
      }
      else {
        for (const val of values) {
          finalRow.push({
            label: val,
            key: currentPath.concat(val).join(delimiter),
          });
        }
      }
    }
  }

  pushLeafLabelsWithObjects(tree);
  nestedHeaders.push(finalRow);

  return nestedHeaders;
}

// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  fetchProgressHistoryData();
});
</script>

<template>
  <div class="h-[calc(100vh-200px)] w-full">
    <BiHandsontable
      v-if="!state.loading && !state.error && state.table_data.length > 0"
      ref="bi_table_ref"
      :bi-table-id="`preview-table-${props.id}`"
      :data="state.table_data"
      :columns="table_columns"
      :column-config="column_config"
      :nested-headers="state.nested_headers"
      :nested-rows="state.nested_rows"
      :show-skeleton-loader="state.loading"
      :row-headers="true"
      class="h-full"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
