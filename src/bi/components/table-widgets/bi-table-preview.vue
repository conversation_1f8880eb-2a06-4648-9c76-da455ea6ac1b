<script setup>
import <PERSON> from 'papa<PERSON><PERSON>';
import { v4 as uuidv4 } from 'uuid';
import { computed, onMounted, reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';
import BiHandsontable from './bi-handsontable.vue';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  id: {
    type: String,
    default: () => uuidv4(),
  },
  dataConfig: {
    type: Object,
    default: () => ({}),
  },
});

const bi_store = useBiStore();

// ---------------------------------- State --------------------------------- //
const state = reactive({
  loading: false,
  error: null,
  csv_data: [],
  columns: [],
  processed_data: [],
  nested_headers: [],
  leaf_level_columns: [],
  column_config: [],
});

async function fetchProgressHistoryData() {
  try {
    state.loading = true;
    state.error = null;

    // Fetch the CSV file from the public directory
    const csvPath = '/progress-history.csv';

    // For demo purposes, we'll simulate fetching from the actual file
    // In a real implementation, you might fetch from an API endpoint
    const response = await fetch(csvPath);

    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }

    const csvText = await response.text();

    // Parse CSV using Papa Parse
    const parseResult = Papa.parse(csvText, {
      header: true,
      skipEmptyLines: true,
      transformHeader: header => header.trim(),
    });

    if (parseResult.errors.length > 0) {
      console.warn('CSV parsing warnings:', parseResult.errors);
    }

    // Extract columns from the first row (headers)
    state.columns = parseResult.meta.fields || [];
    bi_store.table_config.columns = state.columns;

    // Process the data
    state.csv_data = parseResult.data.slice(0, 50000);

    // Apply any data filtering/processing based on dataConfig
    state.processed_data = processDataWithConfig(state.csv_data);
  }
  catch (error) {
    console.error('Error fetching progress history data:', error);
    state.error = error.message;
  }
  finally {
    state.loading = false;
  }
}

function processDataWithConfig(data) {
  // Apply filtering, sorting, or other transformations based on dataConfig
  const processed_data = [...data];
  state.leaf_level_columns = state.columns.map(column => ({
    data: column,
    title: column,
    type: 'text',
    readOnly: true,
    width: props.dataConfig[column]?.width || 150,
  }));

  return processed_data;
}

// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  fetchProgressHistoryData();
});
</script>

<template>
  <div class="h-[calc(100vh-200px)] w-full">
    <BiHandsontable
      v-if="!state.loading && !state.error && state.processed_data.length > 0"
      :bi-table-id="`preview-table-${props.id}`"
      :data="state.processed_data"
      :columns="state.leaf_level_columns"
      :column-config="state.column_config"
      :show-skeleton-loader="state.loading"
      :row-headers="true"
      class="h-full"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
