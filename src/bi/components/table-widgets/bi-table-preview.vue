<script setup>
import <PERSON> from 'papa<PERSON><PERSON>';
import { v4 as uuidv4 } from 'uuid';
import { computed, onMounted, reactive } from 'vue';
import { useBiStore } from '~/bi/store/bi.store';
import BiHandsontable from './bi-handsontable.vue';

// ---------------------------------- Props --------------------------------- //
const props = defineProps({
  id: {
    type: String,
    default: () => uuidv4(),
  },
  dataConfig: {
    type: Object,
    default: () => ({}),
  },
});

const bi_store = useBiStore();

// ---------------------------------- State --------------------------------- //
const state = reactive({
  loading: false,
  error: null,
  csv_data: [],
  columns: [],
  processed_data: [],
  nested_headers: [],
  leaf_level_columns: [],
  column_config: {},
});

async function fetchProgressHistoryData() {
  try {
    state.loading = true;
    state.error = null;

    // Fetch the CSV file from the public directory
    const csvPath = '/progress-history.csv';

    // For demo purposes, we'll simulate fetching from the actual file
    // In a real implementation, you might fetch from an API endpoint
    const response = await fetch(csvPath);

    if (!response.ok) {
      throw new Error(`Failed to fetch CSV: ${response.statusText}`);
    }

    const csvText = await response.text();

    // Parse CSV using Papa Parse
    const parseResult = Papa.parse(csvText, {
      header: true,
      skipEmptyLines: true,
      transformHeader: header => header.trim().replace(/\n/g, ''),
    });

    if (parseResult.errors.length > 0) {
      console.warn('CSV parsing warnings:', parseResult.errors);
    }

    // Extract columns from the first row (headers)
    state.columns = parseResult.meta.fields || [];
    bi_store.table_config.columns = state.columns;

    // Process the data
    state.csv_data = parseResult.data.slice(0, 50000);

    // Apply any data filtering/processing based on dataConfig
    state.processed_data = processDataWithConfig(state.csv_data);
    console.log(state.processed_data, state.columns, state.nested_headers, state);
  }
  catch (error) {
    console.error('Error fetching progress history data:', error);
    state.error = error.message;
  }
  finally {
    state.loading = false;
  }
}

function processDataWithConfig(data) {
  if (!data || data.length === 0) {
    return [];
  }

  const config = bi_store.table_preview_config;

  // Extract column values from config
  const rows = config.rows?.map(row => row.value).filter(Boolean) || [];
  const columns = config.columns?.map(col => col.value).filter(Boolean) || [];
  const row_groups = config.row_groups?.map(group => group.value).filter(Boolean) || [];
  const column_groups = config.column_groups?.map(group => group.value).filter(Boolean) || [];

  // If no configuration is set, return original data
  if (rows.length === 0 && columns.length === 0 && row_groups.length === 0 && column_groups.length === 0) {
    state.leaf_level_columns = state.columns.map(column => ({
      data: column,
      title: column,
      type: 'text',
      readOnly: true,
      width: props.dataConfig[column]?.width || 150,
    }));
    return data;
  }

  // Process the data based on configuration
  let processed_data = [...data];

  // Create pivot-like structure
  if (row_groups.length > 0 || column_groups.length > 0) {
    processed_data = createPivotStructure(data, {
      rows,
      columns,
      row_groups,
      column_groups,
    });
  }
  else {
    // Simple column filtering
    const visible_columns = [...rows, ...columns];
    if (visible_columns.length > 0) {
      processed_data = data.map((row) => {
        const filtered_row = {};
        visible_columns.forEach((col) => {
          if (Object.prototype.hasOwnProperty.call(row, col)) {
            filtered_row[col] = row[col];
          }
        });
        return filtered_row;
      });
    }
  }

  return processed_data;
}

function createPivotStructure(data, config) {
  const { rows, columns, row_groups, column_groups } = config;

  // Build nested headers and columns first
  buildNestedHeaders(config);

  // Group data by row groups first
  const grouped_data = {};

  data.forEach((row) => {
    // Create group key from row_groups
    const group_key = row_groups.map(col => row[col] || '').join('|');

    if (!grouped_data[group_key]) {
      grouped_data[group_key] = {
        group_info: {},
        rows: [],
      };

      // Store group information
      row_groups.forEach((col) => {
        grouped_data[group_key].group_info[col] = row[col];
      });
    }

    grouped_data[group_key].rows.push(row);
  });

  // Get unique column group combinations
  const column_group_values = getUniqueColumnGroupValues(data, column_groups);

  // Create the final structure
  const result = [];

  // Process each group
  Object.entries(grouped_data).forEach(([_group_key, group_data]) => {
    // Get unique row combinations within this group
    const unique_row_values = getUniqueValues(group_data.rows, rows);

    unique_row_values.forEach((row_combination) => {
      const result_row = {
        ...group_data.group_info,
        ...row_combination,
      };

      // For each column group combination, add the column data
      column_group_values.forEach((group_combination) => {
        const group_label = column_groups.map(col => group_combination[col]).join(' - ');

        columns.forEach((col) => {
          const column_key = `${group_label}_${col}`;

          // Find matching data for this specific combination
          const matching_rows = group_data.rows.filter((r) => {
            // Match row criteria
            const row_match = rows.every(row_col => r[row_col] === row_combination[row_col]);
            // Match column group criteria
            const col_group_match = column_groups.every(col_group => r[col_group] === group_combination[col_group]);

            return row_match && col_group_match;
          });

          if (matching_rows.length > 0) {
            result_row[column_key] = matching_rows[0][col];
          }
          else {
            result_row[column_key] = '';
          }
        });
      });

      result.push(result_row);
    });
  });

  return result;
}

function getUniqueValues(data, columns) {
  const unique_combinations = new Map();

  data.forEach((row) => {
    const combination = {};
    columns.forEach((col) => {
      combination[col] = row[col];
    });

    const key = JSON.stringify(combination);
    if (!unique_combinations.has(key)) {
      unique_combinations.set(key, combination);
    }
  });

  return Array.from(unique_combinations.values());
}

function buildNestedHeaders(config) {
  const { rows, columns, row_groups, column_groups } = config;

  if (column_groups.length > 0) {
    // Get unique values for column groups to create the nested structure
    const column_group_values = getUniqueColumnGroupValues(state.csv_data, column_groups);

    // Create nested headers
    const first_level = [];
    const second_level = [];
    const leaf_columns = [];

    // Add row group and row columns first (these don't have nested headers)
    [...row_groups, ...rows].forEach((col) => {
      first_level.push({ label: col, colspan: 1 });
      second_level.push({ label: col, colspan: 1 });
      leaf_columns.push({
        data: col,
        title: col,
        type: 'text',
        readOnly: true,
        width: props.dataConfig[col]?.width || 150,
      });
    });

    // Create nested structure for column groups
    column_group_values.forEach((group_combination) => {
      // Create the group header (e.g., "Phase-1")
      const group_label = column_groups.map(col => group_combination[col]).join(' - ');
      first_level.push({ label: group_label, colspan: columns.length });

      // Add the data columns under this group (e.g., "Work done", "Scope")
      columns.forEach((col) => {
        second_level.push({ label: col, colspan: 1 });

        // Create unique column key for this combination
        const column_key = `${group_label}_${col}`;
        leaf_columns.push({
          data: column_key,
          title: col,
          type: 'text',
          readOnly: true,
          width: props.dataConfig[col]?.width || 150,
        });
      });
    });

    state.nested_headers = [first_level, second_level];
    state.leaf_level_columns = leaf_columns;
  }
  else {
    // No column groups - simple structure
    state.nested_headers = [];
    const all_columns = [...row_groups, ...rows, ...columns];
    state.leaf_level_columns = all_columns.map(column => ({
      data: column,
      title: column,
      type: 'text',
      readOnly: true,
      width: props.dataConfig[column]?.width || 150,
    }));
  }
}

function getUniqueColumnGroupValues(data, column_groups) {
  const unique_combinations = new Map();

  data.forEach((row) => {
    const combination = {};
    column_groups.forEach((col) => {
      combination[col] = row[col];
    });

    const key = JSON.stringify(combination);
    if (!unique_combinations.has(key)) {
      unique_combinations.set(key, combination);
    }
  });

  return Array.from(unique_combinations.values());
}

// -------------------------------- Lifecycle ------------------------------- //
onMounted(() => {
  fetchProgressHistoryData();
});

watch(() => bi_store.table_preview_config, () => {
  state.processed_data = processDataWithConfig(state.csv_data);
  console.log(state);
}, { deep: true });
</script>

<template>
  <div class="h-[calc(100vh-200px)] w-full">
    <BiHandsontable
      v-if="!state.loading && !state.error && state.processed_data.length > 0"
      :bi-table-id="`preview-table-${props.id}`"
      :data="state.processed_data"
      :columns="state.leaf_level_columns"
      :column-config="state.column_config"
      :nested-headers="state.nested_headers"
      :show-skeleton-loader="state.loading"
      :row-headers="true"
      class="h-full"
    />
  </div>
</template>

<style scoped lang="scss">
// Add any custom styles here
</style>
[
    [
        {
            "label": "Activity",
            "colspan": 1
        },
        {
            "label": "Sub Activity",
            "colspan": 1
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 4.02",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 8.39",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.34",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 1.35",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.30",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 8.38",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 8.41",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 4.06",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.29",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.31",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 1.14",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 6.21",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 6.19",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 1.15",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 6.20",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 8.37",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 8.36",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 6.18",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 3.07",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.32",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.28",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 4.05",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 5.26",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 7.45",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 3.10",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 1.13",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 7.46",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 8.40",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 2.33",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 1.16",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 6.23",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 4.04",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 3.08",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 3.09",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 4.01",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 3.11",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 5.27",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 5.24",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 3.12",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 7.42",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 4.03",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 7.44",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 6.22",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 7.43",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 1.17",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 7.47",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 5.49",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 5.48",
            "colspan": 2
        },
        {
            "label": "02b.Mechanical Tracker Updated\n - PCS 5.25",
            "colspan": 2
        }
    ],
    [
        {
            "label": "Activity",
            "colspan": 1
        },
        {
            "label": "Sub Activity",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        },
        {
            "label": "Date",
            "colspan": 1
        },
        {
            "label": "Value",
            "colspan": 1
        }
    ]
]