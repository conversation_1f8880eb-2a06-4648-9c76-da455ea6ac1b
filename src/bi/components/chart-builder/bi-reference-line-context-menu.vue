<script setup>
const props = defineProps({
  referenceLineConfig: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['fieldSelected']);

const state = reactive({
  is_menu_open: false,
  color: null,
  label: null,
  value: null,
  line_style: null,
});

const colors = ['#101828', '#004EEB', '#D92D20', '#DC6803', '#039855', '#7839EE', '#4CA30D', '#0E9384', '#BA24D5', '#E31B54', '#344054', '#2E90FA', '#F97066', '#FDB022', '#32D583', '#A48AFB', '#66C61C', '#2ED3B7', '#E478FA', '#FD6F8E', '#667085', '#84CAFF', '#FDA29B', '#FEC84B', '#6CE9A6', '#DDD6FE', '#85E13A', '#5FE9D0', '#EEAAFD', '#FEA3B4'];

const line_styles = computed(() => {
  return [
    { uid: 'solid', leftSlot: h('div', { style: 'height: 20px; width: 20px; display: flex; align-items: center; justify-content: center;' }, [h('hr', { style: 'border: none; height: 2px; background-color: currentColor; margin: 0; width: 100%;' })]), tooltip_text: 'Solid' },
    { uid: 'dashed', leftSlot: IconHawkStroke, tooltip_text: 'Dashed' },
    { uid: 'dotted', leftSlot: IconHawkDotsHorizontal, tooltip_text: 'Dotted' },
  ];
});

function onFieldSelected(field_name, value) {
  state[field_name] = value;
  emit('fieldSelected', { [field_name]: value });
}

function onMenuOpen() {
  Object.keys(props.referenceLineConfig).forEach((key) => {
    state[key] = props.referenceLineConfig[key];
  });
  state.is_menu_open = true;
}
</script>

<template>
  <div class="flex items-center gap-1">
    <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0" @open="onMenuOpen" @close="state.is_menu_open = false">
      <template #trigger>
        <IconHawkDotsVertical class="w-4 h-4" />
      </template>
      <template #content>
        <div class="px-3.5 py-3">
          <div class="mb-3">
            <h3 class="text-lg font-semibold text-gray-900 mb-3">
              Configure reference line
            </h3>
            <hr class="-mx-3.5">
          </div>
          <Vueform
            v-if="state.is_menu_open"
            size="sm"
            :columns="{
              default: { container: 12, label: 4, wrapper: 12 },
              sm: { container: 12, label: 4, wrapper: 12 },
              md: { container: 12, label: 4, wrapper: 12 },
            }"
            :add-classes="{
              FormElements: {
                container: '!gap-y-4',
              },
            }"
          >
            <div class="col-span-12">
              <div class="flex items-center gap-3">
                <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0">
                  <template #trigger>
                    <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: state.color }" />
                  </template>
                  <template #content>
                    <div class="flex flex-wrap gap-x-3 gap-y-2 w-[355px] p-3">
                      <div
                        v-for="color in colors"
                        :key="color"
                        class="p-0.5 rounded-full border border-transparent cursor-pointer"
                        :class="{ '!border-gray-300': state.color === color }"
                        @click="onFieldSelected('color', color)"
                      >
                        <div
                          class="w-4 h-4 rounded-full"
                          :style="{ backgroundColor: color }"
                        />
                      </div>
                    </div>
                  </template>
                </HawkMenu>
                <div class="flex-1">
                  <TextElement
                    name="label"
                    :default="state.label"
                    @change="onFieldSelected('label', $event)"
                  />
                </div>
              </div>
            </div>

            <TextElement
              name="value"
              label="Value"
              :default="state.value"
              placeholder="Enter value"
              :add-classes="{
                TextElement: {
                  inputContainer: '!h-7',
                },
              }"
              @change="onFieldSelected('value', $event)"
            />

            <div class="col-span-12">
              <div class="grid grid-cols-12 gap-0 items-center">
                <label class="col-span-4 text-sm font-medium text-gray-700">Line style</label>
                <div class="col-span-8">
                  <HawkButtonGroup
                    :items="line_styles"
                    icon
                    size="sm"
                    :active_item="state.line_style"
                    class="w-fit"
                    @select="onFieldSelected('line_style', $event.uid)"
                  />
                </div>
              </div>
            </div>
          </Vueform>
        </div>
      </template>
    </HawkMenu>
  </div>
</template>
