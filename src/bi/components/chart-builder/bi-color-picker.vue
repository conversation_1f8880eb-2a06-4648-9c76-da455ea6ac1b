<script setup>
const props = defineProps({
  activeColor: {
    type: String,
    default: null,
  },
});

const emit = defineEmits(['colorSelected']);

const colors = ['#101828', '#004EEB', '#D92D20', '#DC6803', '#039855', '#7839EE', '#4CA30D', '#0E9384', '#BA24D5', '#E31B54', '#344054', '#2E90FA', '#F97066', '#FDB022', '#32D583', '#A48AFB', '#66C61C', '#2ED3B7', '#E478FA', '#FD6F8E', '#667085', '#84CAFF', '#FDA29B', '#FEC84B', '#6CE9A6', '#DDD6FE', '#85E13A', '#5FE9D0', '#EEAAFD', '#FEA3B4'];
</script>

<template>
  <HawkMenu position="fixed" additional_trigger_classes="mt-1.5 p-0 m-0 !ring-0 !border-0 focus:!ring-0">
    <template #trigger>
      <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: props.activeColor }" />
    </template>
    <template #content>
      <div class="flex flex-wrap gap-x-3 gap-y-2 w-[355px] p-3">
        <div
          v-for="color in colors"
          :key="color"
          class="p-0.5 rounded-full border border-transparent cursor-pointer"
          :class="{ '!border-gray-300': props.activeColor === color }"
          @click="emit('colorSelected', color)"
        >
          <div
            class="w-4 h-4 rounded-full"
            :style="{ backgroundColor: color }"
          />
        </div>
      </div>
    </template>
  </HawkMenu>
</template>
