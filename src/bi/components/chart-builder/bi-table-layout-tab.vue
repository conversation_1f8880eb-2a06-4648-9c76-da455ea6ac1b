<script setup>
import { useBiStore } from '~/bi/store/bi.store';

const bi_store = useBiStore();

const columns_list = computed(() => {
  const hidden_columns = bi_store.table_preview_config.rows.map(row => row?.value);
  hidden_columns.push(...bi_store.table_preview_config.columns.map(column => column?.value));
  hidden_columns.push(...bi_store.table_preview_config.row_groups.map(group => group?.value));
  hidden_columns.push(...bi_store.table_preview_config.column_groups.map(group => group?.value));
  return bi_store.table_config.columns.map(column => ({ value: column, label: column, disabled: hidden_columns.includes(column) })); ;
});
</script>

<template>
  <Vueform
    v-model="bi_store.table_preview_config"
    sync
    :columns="{
      lg: {
        container: 12,
        label: 3,
        wrapper: 12,
      },
    }"
  >
    <div class="col-span-12">
      <div class="text-sm font-medium mb-1">
        Rows
      </div>
      <ListElement name="rows" class="mb-4">
        <template #default="{ index }">
          <ObjectElement
            :name="index"
          >
            <SelectElement
              name="value"
              :items="columns_list"
              :native="false"
              :can-clear="false"
              :can-deselect="false"
            />
          </ObjectElement>
        </template>
      </ListElement>
      <div class="text-sm font-medium mb-1">
        Columns
      </div>
      <ListElement name="columns" class="mb-4">
        <template #default="{ index }">
          <ObjectElement
            :name="index"
          >
            <SelectElement
              name="value"
              :items="columns_list"
              :native="false"
              :can-clear="false"
              :can-deselect="false"
            />
          </ObjectElement>
        </template>
      </ListElement>
      <div class="text-sm font-medium mb-1">
        Row groups
      </div>
      <ListElement name="row_groups" class="mb-4">
        <template #default="{ index }">
          <ObjectElement
            :name="index"
          >
            <SelectElement
              name="value"
              :items="columns_list"
              :native="false"
              :can-clear="false"
              :can-deselect="false"
            />
          </ObjectElement>
        </template>
      </ListElement>
      <div class="text-sm font-medium mb-1">
        Column groups
      </div>
      <ListElement name="column_groups" class="mb-4">
        <template #default="{ index }">
          <ObjectElement
            :name="index"
          >
            <SelectElement
              name="value"
              :items="columns_list"
              :native="false"
              :can-clear="false"
              :can-deselect="false"
            />
          </ObjectElement>
        </template>
      </ListElement>
    </div>
  </Vueform>
</template>
