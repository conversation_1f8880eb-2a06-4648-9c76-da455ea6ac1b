<script setup>
const props = defineProps({
  chartType: {
    type: String,
    required: true,
  },
  chartConfig: {
    type: Object,
    required: true,
  },
});

const state = reactive({
  are_more_options_visible: false,
});

const scale_types = [
  ['linear', 'Linear'],
  ['log', 'Logarithmic'],
  ['power', 'Power'],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
  };
});

const tick_label_types = [
  ['show', 'Show'],
  ['hide', 'Hide'],
  ['compact', 'Compact'],
  ['rotate45', 'Rotate 45°'],
  ['rotate90', 'Rotate 90°'],
].map((item) => {
  return {
    value: item[0],
    label: item[1],
  };
});

function getAxis(type) {
  // type = 'category' || 'value'
  if (props.chartConfig.orientation === 'horizontal') {
    if (type === 'category')
      return 'y';
    else return 'x';
  }
  else {
    if (type === 'category')
      return 'x';
    else return 'y';
  }
}
</script>

<template>
  <template v-if="['bar_chart', 'line_chart', 'area_chart'].includes(props.chartType)">
    <div class="text-sm font-semibold text-gray-900 -mb-3">
      Axis names
    </div>
    <div class="flex gap-3">
      <TextElement
        name="category_axis_name"
        :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
        placeholder="Enter label"
      />
      <TextElement
        name="value_axis_name"
        :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
        placeholder="Enter label"
      />
    </div>
    <ToggleElement
      v-if="getAxis('value') === 'y'"
      name="dual_y_axis"
      label="Dual Y-axis"
      :columns="{
        default: { container: 12, label: 11, wrapper: 12 },
        sm: { container: 12, label: 11, wrapper: 12 },
      }"
    />
    <TextElement
      :conditions="[['dual_y_axis', '==', true]]"
      name="secondary_y_axis"
      label="Secondary Y-axis name"
      placeholder="Enter label"
      class="-mt-3"
    />
    <div class="w-40 cursor-pointer" @click="state.are_more_options_visible = !state.are_more_options_visible">
      <div class="flex items-center text-sm font-semibold text-primary-700">
        <IconHawkChevronRight v-if="!state.are_more_options_visible" class="w-6 h-6" />
        <IconHawkChevronDown v-else class="w-6 h-6" />
        More options
      </div>
    </div>
    <div v-show="state.are_more_options_visible" class="col-span-12 flex flex-col gap-y-5 -mt-3">
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Scale type
      </div>
      <div class="flex gap-3">
        <SelectElement
          name="primary_scale"
          label="Primary"
          :items="scale_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="linear"
          class="w-full"
        />
        <SelectElement
          name="secondary_scale"
          label="Secondary"
          :items="scale_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="linear"
          class="w-full"
        />
      </div>
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Tick labels
      </div>
      <div class="flex gap-3">
        <SelectElement
          name="category_tick_label"
          :label="getAxis('category') === 'x' ? 'X-axis' : 'Y-axis'"
          :items="tick_label_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="show"
          class="w-full"
        />
        <SelectElement
          name="value_tick_label"
          :label="getAxis('value') === 'x' ? 'X-axis' : 'Y-axis'"
          :items="tick_label_types"
          :native="false"
          :can-clear="false"
          :can-deselect="false"
          default="show"
          class="w-full"
        />
      </div>
      <SelectElement
        name="secondary_value_tick_label"
        label="Secondary Y-axis"
        :items="tick_label_types"
        :conditions="[['dual_y_axis', '==', true]]"
        :native="false"
        :can-clear="false"
        :can-deselect="false"
        default="show"
        class="w-1/2 -mt-3"
      />
      <div class="text-sm font-semibold text-gray-900 -mb-3">
        Custom range
      </div>
      <div class="flex gap-3">
        <TextElement
          name="custom_range_min"
          label="Minimum"
          input-type="number"
        />
        <TextElement
          name="custom_range_max"
          label="Maximum"
          input-type="number"
        />
      </div>
    </div>
  </template>
  <template v-else>
    Axes - {{ props.chartType }}
  </template>
</template>
