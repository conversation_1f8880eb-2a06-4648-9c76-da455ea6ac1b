import IconHawkArrowDown from '~icons/hawk/arrow-down';
import IconHawkArrowUp from '~icons/hawk/arrow-up';
import IconHawkBinary from '~icons/hawk/binary';
import IconHawkBoolean from '~icons/hawk/boolean';
import IconHawkBracketsEllipses from '~icons/hawk/brackets-ellipses';
import calendar from '~icons/hawk/calendar';
import IconHawkClock from '~icons/hawk/clock';
import IconHawkFormula from '~icons/hawk/formula';
import IconHawkFunction from '~icons/hawk/function';
import IconHawkHashTwo from '~icons/hawk/hash-two';
import IconHawkInnerJoin from '~icons/hawk/inner-join';
import IconHawkOuterJoin from '~icons/hawk/outer-join';
import IconHawkRightJoin from '~icons/hawk/right-join';
import IconHawkTypeOne from '~icons/hawk/type-one';
import IconHawkLeftJoin from '~icons/hawk/vector-join';

function getIconsForType(type) {
  const icons_type_map = {
    'string': IconHawkTypeOne,
    'text': IconHawkTypeOne,
    'varchar': IconHawkTypeOne,
    'char': IconHawkTypeOne,
    'numeric': IconHawkHashTwo,
    'integer': IconHawkHashTwo,
    'bigint': IconHawkHashTwo,
    'smallint': IconHawkHashTwo,
    'decimal': IconHawkHashTwo,
    'float': IconHawkHashTwo,
    'double': IconHawkHashTwo,
    'real': IconHawkHashTwo,
    'number': IconHawkHashTwo,
    'double precision': IconHawkHashTwo,
    'date': calendar,
    'datetime': calendar,
    'timestamp': calendar,
    'timestamptz': IconHawkClock,
    'boolean': IconHawkBoolean,
    'bool': IconHawkBoolean,
    'interval': IconHawkClock,
    'time': IconHawkClock,
    'binary': IconHawkBinary,
    'bytea': IconHawkBinary,
    'json': IconHawkBracketsEllipses,
    'jsonb': IconHawkBracketsEllipses,
    'uuid': IconHawkHashTwo,
    'id': IconHawkHashTwo,
    'function': IconHawkFunction,
    'formula': IconHawkFormula,
    'ascending': IconHawkArrowUp,
    'descending': IconHawkArrowDown,
    'joins': {
      inner: IconHawkInnerJoin,
      outer: IconHawkOuterJoin,
      right: IconHawkRightJoin,
      left: IconHawkLeftJoin,
    },
  };
  return icons_type_map[type] || IconHawkTypeOne;
}

function getOperatorsForType(type) {
  const operators_type_map = {
    text: [{ label: 'Concat', output_type: 'text' }],
    numeric: [{ label: 'Sum', output_type: 'numeric' }, { label: 'Avg', output_type: 'numeric' }, { label: 'Max', output_type: 'numeric' }, { label: 'Min', output_type: 'numeric' }, { label: 'Count', output_type: 'numeric' }],
    date: [{ label: 'Min', output_type: 'date' }, { label: 'Max', output_type: 'date' }],
  };

  const operators_for_type = {
    integer: operators_type_map.numeric,
    float: operators_type_map.numeric,
    date: operators_type_map.date,
    text: operators_type_map.text,
    numeric: operators_type_map.numeric,
    timestamp: operators_type_map.date,
  };

  return operators_for_type[type] || [];
}

export function useBIQueryBuilder() {
  const constructColumnKeys = (table, column) => `${table.label} -> ${column.label}`;

  return {
    constructColumnKeys,
    getIconsForType,
    getOperatorsForType,
  };
}
