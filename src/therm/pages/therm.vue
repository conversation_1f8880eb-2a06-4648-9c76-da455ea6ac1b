<script setup>
import { ref } from 'vue';
import { useModal } from 'vue-final-modal';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import useEmitter from '~/common/composables/useEmitter';
import { useFormsStore } from '~/forms/store/forms.store';
import MbTilesPopup from '~/terra/components/mb-tiles-popup.vue';
import RestoreView from '~/terra/molecules/restore-view.vue';
import ThermExportPopup from '~/therm/components/therm-export-popup.vue';
import ThermSettingsModal from '~/therm/components/therm-settings-modal.vue';
import { useThermHelperComposable } from '~/therm/composables/helper-composable.js';
import { useOnGenerateExcelClicked } from '~/therm/composables/therm-export-csv';
import { useThermPdfExporter } from '~/therm/composables/therm-pdf-exporter.js';
import { useThermStore } from '../store/therm.store';

const { $t, $toast, auth_store, route, router } = useCommonImports();

const emitter = useEmitter();
const therm_store = useThermStore();
const forms_store = useFormsStore('therm_forms_store');
const { flyToAssociatedFeature } = useThermHelperComposable();
const selected_features = ref(null);

const state = reactive({
  active_tab: null,
  export_as: '',
  is_exporting: false,
  is_pdf_export_complete: false,
  pdf_options: {},
  current_export_file_index: 1,
  total_files_to_export: 1,
  therm_pdf_exporter: null,
});

const therm_settings_popup = useModal({
  component: ThermSettingsModal,
  attrs: {
    onClose() {
      therm_settings_popup.close();
    },
  },
});

const therm_export_popup = useModal({
  component: ThermExportPopup,
  attrs: {
    onClose() {
      therm_export_popup.close();
    },
  },
});
const mb_tiles_popup = useModal({
  component: MbTilesPopup,
  attrs: {
    type: 'therm',
    onClose() {
      mb_tiles_popup.close();
    },
  },
});
const header_tabs = computed(() => ([
  {
    uid: 'therm',
    label: $t('Therm'),
    to: { name: 'therm', params: { ...route.params } },
  },
  {
    uid: 'therm-defects',
    label: $t('Defects'),
    to: { name: 'therm-defects', params: { ...route.params } },
  },
  {
    uid: therm_store.use_tasks ? 'therm-reports' : 'therm-dashboard',
    label: $t('Reports'),
    to: { name: therm_store.use_tasks ? 'therm-reports' : 'therm-dashboard', params: { ...route.params } },
  },
]));

const view_details = computed(() => {
  return {
    active_group: therm_store.active_group?.uid,
    active_projects: therm_store.active_projects?.map(project => project.uid),
    inactive_feature_types: therm_store.inactive_feature_types,
    filters: therm_store.filters,
    show_defect_status_icons: therm_store.show_defect_status_icons,
    active_tab: state.active_tab,
  };
});

function updateActiveTab() {
  state.active_tab = { name: route.name, params: { ...route.params } };
}
function updateRestoreView() {
  if (therm_store.restore_view_config && !therm_store.is_loading) {
    const view_config = JSON.parse(localStorage.getItem(therm_store.restore_view_config.key));
    if (!view_config?.hide_restore_view)
      localStorage.setItem(therm_store.restore_view_config.key, JSON.stringify({ data: view_details.value }));
  }
}

async function getData() {
  try {
    therm_store.loader_enabled = true;
    therm_store.set_ftg_and_update_features_styles({
      uid: route.params.id,
      update_features_styles: false,
    });

    await therm_store.set_container({
      uid: route.params.id,
      initial_load: true,
    });
    await therm_store.get_status_configs(route.params.id);
    if (route.query?.metadata)
      await flyToAssociatedFeature('therm');

    therm_store.loader_enabled = false;
  }
  catch (err) {
    logger.error(err);
    therm_store.loader_enabled = false;
    if (route.query?.metadata) {
      $toast({
        title: $t('Location not found'),
        text: $t('Can not navigate to the location. You don\'t have access, or the location is no longer available'),
        type: 'warning',
      });
    }
  }
}

function resizeMap() {
  if (therm_store.map) {
    setTimeout(() => {
      therm_store.map.resize();
    }, 250);
  }
}

function openSettingsModal() {
  const options = {
    use_tasks: auth_store.has_tasks_access && therm_store.use_tasks,
    show_defect_status_icons: therm_store.show_defect_status_icons,
    disable_use_tasks: !auth_store.has_tasks_access || !auth_store.check_split('therm_tasks_config'),
  };
  therm_settings_popup.patchOptions({
    attrs: {
      ...options,
      on_save: async (formData, { data }) => {
        if (options.use_tasks !== data.use_tasks)
          await therm_store.updateUseTasks({ useTasks: data.use_tasks });
        therm_store.show_defect_status_icons = data.use_tasks ? true : data.show_defect_status_icons;
        therm_settings_popup.close();
      },
    },
  });
  therm_settings_popup.open();
}

function openExportPopup(export_type = '') {
  therm_export_popup.patchOptions({
    attrs: {
      exportType: export_type,
      onExport({ export_as, pdf_options }) {
        state.export_as = export_as;
        state.pdf_options = pdf_options;
      },
    },
  });
  therm_export_popup.open();
}

function isExporting() {
  return state.is_exporting;
}

async function onGenerateExcelClicked(e, issues = []) {
  state.is_exporting = true;
  await useOnGenerateExcelClicked(issues, route, isExporting);
}

async function onGeneratePDFClicked() {
  state.is_pdf_export_complete = false;
  state.is_exporting = true;
  state.therm_pdf_exporter = useThermPdfExporter((payload) => {
    state.current_export_file_index = payload.current_export_file_index;
    state.total_files_to_export = payload.total_files_to_export;
  }, isExporting, state.pdf_options);
  await state.therm_pdf_exporter.exportPDF(selected_features.value);
}

function onExportClose() {
  state.is_exporting = false;
  state.export_as = '';
  selected_features.value = null;
}

async function updateDetails(data) {
  try {
    therm_store.loader_enabled = true;
    therm_store.selected_features = [];

    // Making all active projects inactive
    Object.values(therm_store.active_group.projects).forEach((project) => {
      project.is_active = false;
      therm_store.container.groups[project.group].projects[project.uid] = project;
    });

    const group = therm_store.container.groups[data.active_group];
    group.is_active = true;
    await therm_store.set_active_group({ group });

    const response = await Promise.all(
      data.active_projects.map((project_uid) => {
        return therm_store.set_therm_active_project({
          project: therm_store.active_group.projects?.[project_uid],
          is_active: true,
          in_toggling_all_projects_context: true,
        });
      }),
    );

    therm_store.last_selected_project = response[response.length - 1];
    therm_store.filters = data.filters;
    therm_store.inactive_feature_types = data.inactive_feature_types;
    therm_store.show_defect_status_icons = data.show_defect_status_icons;

    await therm_store.fetch_therm_tasks();
    if (data.active_tab.name)
      router.push(data.active_tab);
    if (data.active_tab.name === 'therm')
      therm_store.update_map_features();

    therm_store.loader_enabled = false;
  }
  catch (err) {
    logger.error(err);
    therm_store.loader_enabled = false;
  }
}

emitter.on('therm_pdf_export_action', async (features) => {
  selected_features.value = features;
  openExportPopup('PDF');
});

onMounted(async () => {
  await getData();
  therm_store.is_data_loaded = true;
  updateActiveTab();
  window.onbeforeunload = function () {
    updateRestoreView();
    return undefined;
  };
});

onBeforeUnmount(() => {
  emitter.off('therm_pdf_export_action');
  updateRestoreView();
  therm_store.reset_store();
  window.onbeforeunload = null;
});

watch(() => therm_store.show_request_mbtiles_popup?.requested_reports, () => {
  if (therm_store.show_request_mbtiles_popup?.resolved_all_requests && Object.keys(therm_store.show_request_mbtiles_popup?.requested_reports || {}).length)
    mb_tiles_popup.open();
}, {
  deep: true,
});
watch(() => therm_store.polygon, async (polygon) => {
  if (polygon) {
    await forms_store.set_forms({
      query: {
        stage: 'THERM',
        status: 'published',
        polygon,
        is_child: true,
        all_access: true,
        asset_uid: route.params.asset_id,
      },
    });
  }
});
watch(() => route.name, (val) => {
  if (header_tabs.value.find(tab => tab.uid === val))
    updateActiveTab();
  if (val === 'therm')
    resizeMap();
});
watch(() => route.params.asset_id, (val) => {
  if (val)
    router.push({ name: 'maps-list', params: { type: 'maps-list', asset_id: val } });
});
</script>

<template>
  <div>
    <HawkExportToast v-if="state.export_as === 'Excel'" :submit="onGenerateExcelClicked" :progress_text="$t('Exporting to Excel')" :completed_text="$t('Exported Excel')" @cancel="onExportClose" @close="onExportClose" />
    <HawkExportToast v-if="state.export_as === 'PDF'" :submit="onGeneratePDFClicked" @cancel="onExportClose" @complete="onExportClose" @close="onExportClose">
      <template v-if="!state.is_pdf_export_complete" #title>
        <div>
          {{ $t('Exporting PDF') }}<span v-if="state.total_files_to_export > 1">: {{ state.current_export_file_index }}/{{ state.total_files_to_export }}</span>
          <div v-if="auth_store.is_internal_user && !!state.therm_pdf_exporter?.images_skipped" class="text-xs mt-0.5">
            {{ state.therm_pdf_exporter?.images_skipped }} images skipped
          </div>
        </div>
      </template>
      <template v-else #title>
        {{ $t('Exported PDF') }}
      </template>
    </HawkExportToast>
    <div v-if="therm_store.loader_enabled" class="absolute w-full h-[calc(100vh-70px)] z-50 bg-gray-100 opacity-[0.9] flex justify-center items-center">
      <HawkLoader />
    </div>
    <div class="therm-routes h-[68px] flex flex-col justify-center ">
      <HawkPageHeader :title="$t('Therm')" class="text-gray-900  font-semibold text-lg">
        <template #left>
          <HawkPageHeaderTabs :tabs="header_tabs" :active_item="route.name" />
        </template>
        <template #right>
          <div class="flex items-center">
            <HawkButton class="font-semibold text-gray-700 text-sm" type="outlined" @click="openExportPopup()">
              {{ $t('Export') }}
            </HawkButton>
            <HawkButton v-if="auth_store.is_internal_user" type="outlined" icon size="sm" class="ml-2" @click="openSettingsModal">
              <IconHawkSettingsOne />
            </HawkButton>
          </div>
        </template>
      </HawkPageHeader>
    </div>
  </div>
  <div />
  <RestoreView
    :restore_view_config="therm_store.restore_view_config"
    @handle-restore="updateDetails($event)"
  />
  <div class="therm flex m-0 h-[calc(100vh-133px)]">
    <ThermLeftSidebar
      v-if="!['therm-compare', 'therm-dashboard'].includes(route.name)"
      class="flex-grow p-0"
    />
    <keep-alive v-if="route.name !== 'therm-compare'">
      <router-view class="flex-grow p-0 relative" />
    </keep-alive>
    <router-view v-else class="flex-grow p-0 relative" />
  </div>
</template>

<style lang="scss">
  #therm-map {
  background: rgb(215, 215, 215);
  position: absolute;
  width: 100%;
  height: 100%;
  transition: all 0.3s;
}
</style>
